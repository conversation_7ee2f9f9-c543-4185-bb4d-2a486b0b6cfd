import React, { useState, useRef, useEffect } from 'react';
import { SafeAreaView, View, Text, TextInput, TouchableOpacity, StyleSheet, Alert, Dimensions, Keyboard, ActivityIndicator } from 'react-native';
import { useLocalSearchParams, router } from 'expo-router';
import { useAuth } from '../../context/auth-context';
import { StatusBar } from 'expo-status-bar';
import { Colors } from '../../constants/colors';

const { width } = Dimensions.get('window');
const isMobile = width < 768;

const VerifyOtp = () => {
  const { userId, requestId } = useLocalSearchParams();
  const [otp, setOtp] = useState(['', '', '', '', '']);
  const [verifying, setVerifying] = useState(false);
  const inputRefs = useRef([]);
  const { verifyOtp, isLoading } = useAuth();
  
  useEffect(() => {
    setTimeout(() => {
      inputRefs.current[0]?.focus();
    }, 100);
  }, []);

  const handleVerifyOtp = async () => {
    Keyboard.dismiss();
    const otpValue = otp.join('');
    
    if (otpValue.length !== 5) {
      Alert.alert('Invalid OTP', 'Please enter all 5 digits of the OTP');
      return;
    }
    
    setVerifying(true);
    
    try {
      const isVerified = await verifyOtp(userId, requestId, otpValue);
      if (isVerified) {
        router.replace('/');
      }
    } catch (error) {
      Alert.alert('Error', error.message);
    } finally {
      setVerifying(false);
    }
  };
  
  const handleChangeText = (text, index) => {
    if (!/^\d*$/.test(text)) return;
    
    const newOtp = [...otp];
    newOtp[index] = text;
    setOtp(newOtp);
    
    if (text && index < 4) {
      inputRefs.current[index + 1]?.focus();
    } else if (text && index === 4) {
      inputRefs.current[index]?.blur();
      if (newOtp.every(digit => digit !== '')) {
        Keyboard.dismiss();
      }
    }
  };
  
  const handleKeyPress = (e, index) => {
    if (e.nativeEvent.key === 'Backspace' && !otp[index] && index > 0) {
      inputRefs.current[index - 1]?.focus();
    }
  };
  
  const handleFocus = (index) => {
    inputRefs.current[index]?.setNativeProps({ selection: { start: 0, end: 1 } });
  };

  const isButtonLoading = verifying || isLoading;

  return (
    <SafeAreaView style={styles.container}>
      <StatusBar style="dark" />
      <View style={styles.headerContainer}>
        <Text style={styles.title}>Enter Verification Code</Text>
        <Text style={styles.subtitle}>
        Please enter the OTP sent to your registered mobile number.
        </Text>
      </View>
      
      <View style={styles.otpContainer}>
        {otp.map((digit, index) => (
          <TextInput
            key={index}
            ref={(ref) => (inputRefs.current[index] = ref)}
            style={[
              styles.otpInput,
              digit && styles.otpInputFilled,
              isButtonLoading && styles.otpInputDisabled
            ]}
            keyboardType="number-pad"
            maxLength={1}
            value={digit}
            onChangeText={(text) => handleChangeText(text, index)}
            onKeyPress={(e) => handleKeyPress(e, index)}
            onFocus={() => handleFocus(index)}
            selectTextOnFocus={true}
            editable={!isButtonLoading}
          />
        ))}
      </View>
      
      <TouchableOpacity 
        style={[
          styles.verifyButton,
          (otp.join('').length !== 5 || isButtonLoading) && styles.verifyButtonDisabled
        ]} 
        onPress={handleVerifyOtp} 
        disabled={isButtonLoading || otp.join('').length !== 5}
      >
        {isButtonLoading ? (
          <View style={styles.loadingContainer}>
            <ActivityIndicator size="small" color="#fff" />
            <Text style={[styles.buttonText, styles.loadingText]}>VERIFYING</Text>
          </View>
        ) : (
          <Text style={styles.buttonText}>VERIFY</Text>
        )}
      </TouchableOpacity>
    </SafeAreaView>
  );
};

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.background,
    alignItems: 'center',
    justifyContent: 'center',
    padding: 20,
  },
  headerContainer: {
    alignItems: 'center',
    marginBottom: 40,
  },
  title: {
    fontSize: isMobile ? 28 : 34,
    fontWeight: '700',
    fontFamily: 'Roboto_bold',
    color: Colors.black,
    letterSpacing: 0.5,
  },
  subtitle: {
    fontSize: isMobile ? 15 : 17,
    color: Colors.lightText,
    fontFamily: 'Roboto',
    marginTop: 12,
    textAlign: 'center',
    maxWidth: 400,
    lineHeight: 22,
    padding:20,
  },
  otpContainer: {
    flexDirection: 'row',
    justifyContent: 'center',
    marginBottom: 40,
  },
  otpInput: {
    width: isMobile ? 48 : 58,
    height: isMobile ? 58 : 68,
    borderWidth: 2,
    borderColor: Colors.border,
    borderRadius: 12,
    textAlign: 'center',
    fontSize: isMobile ? 24 : 28,
    marginHorizontal: 8,
    color: Colors.black,
    backgroundColor: Colors.background,
    fontWeight: '600',
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 2 },
    shadowOpacity: 0.1,
    shadowRadius: 4,
    elevation: 2,
    fontFamily: 'Roboto_bold',
  },
  otpInputFilled: {
    borderColor: Colors.primary,
    backgroundColor: Colors.background,
  },
  otpInputDisabled: {
    opacity: 0.7,
    backgroundColor: '#f0f0f0',
  },
  verifyButton: {
    backgroundColor: Colors.primary,
    paddingVertical: isMobile ? 14 : 16,
    paddingHorizontal: isMobile ? 50 : 70,
    borderRadius: 50,
    alignItems: 'center',
    elevation: 0,
  },
  verifyButtonDisabled: {
    backgroundColor: Colors.disabled,
    shadowOpacity: 0,
    elevation: 0,
  },
  buttonText: {
    color: '#fff',
    fontSize: isMobile ? 17 : 18,
    fontWeight: '600',
    letterSpacing: 0.5,
  },
  loadingContainer: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
  },
  loadingText: {
    marginLeft: 10,
  },
});

export default VerifyOtp;