import React, { useState, useEffect, useCallback, useMemo } from "react";
import {
  View,
  Text,
  useWindowDimensions,
  TouchableOpacity,
  StyleSheet,
  FlatList,
  ActivityIndicator,
  Alert,
  TextInput,
} from "react-native";
import MaterialCommunityIcons from "@expo/vector-icons/MaterialCommunityIcons";
import { GestureHandlerRootView } from 'react-native-gesture-handler';
import { useAuth } from "../../../../context/auth-context";
import { router, useFocusEffect, useRouter } from 'expo-router';
import { transformUrl } from "../../../../utils/transformUrl";
import PhotoGrid from "../../../../components/Smalls/PhotoGrid";
import LabModal from "../../../../components/Larges/LabModal";
import CourtModal from "../../../../components/Larges/CourtModal";
import LocationViewer from "../../../../components/Larges/LocationViewer";
import SuccessScreen from '../../../../components/Smalls/SuccessScreen';
import EvidencesCards from "../../../../components/Larges/EvidencesCards";
import CommentsSection from "../../../../components/Larges/CommentsSection";
import EvidenceProcessDemo from "../../../../components/Larges/EvidenceProcessDemo";
import { Colors } from "../../../../constants/colors";
import { apiService } from "../../../../services/api";
import { validationService } from "../../../../services/validation";


const Tab1 = ({ caseid, changeTab }) => {
  const { width } = useWindowDimensions();
  const [premisesImages, setPremisesImages] = useState([]);
  const [caseDetails, setCaseDetails] = useState(null);
  const [evidences, setEvidences] = useState([]);
  const [isLoading, setIsLoading] = useState(true);
  const [error, setError] = useState(null);
  const { token } = useAuth();  
  // console.log(token);
  const [isModalVisible, setIsModalVisible] = useState(false);
  const [isCourtModalVisible, setIsCourtModalVisible] = useState(false);
  const [currentEvidenceId, setCurrentEvidenceId] = useState(null);
  const [isSubmitting, setIsSubmitting] = useState(false);

  // Selection functionality
  const [isSelectionMode, setIsSelectionMode] = useState(false);
  const [selectedEvidenceIds, setSelectedEvidenceIds] = useState([]);
  const [isSuccessVisible, setIsSuccessVisible] = useState(false);
  const [forensicQr, setForensicQr] = useState(null);
  const [comments, setComments] = useState([]);
  
  // Edit FIR Number functionality
  const [isEditingFir, setIsEditingFir] = useState(false);
  const [editedFirNumber, setEditedFirNumber] = useState('');
  
  // Location viewer functionality
  const [isLocationViewerVisible, setIsLocationViewerVisible] = useState(false);
  
  // Demo functionality
  const [isDemoVisible, setIsDemoVisible] = useState(false);

  const fetchData = useCallback(async () => {
    if (!caseid || !token) {
      setIsLoading(false);
      setError('Missing case ID or authentication token');
      return;
    }

    setIsLoading(true);
    setError(null);

    try {
      const result = await apiService.fetchCaseDetails(token, caseid);
     console.log('Case Details API response:', JSON.stringify(result, null, 2));
      if (!result?.data) {
        throw new Error('Invalid data format received from server');
      }

      const premisesImageUrls = result.data.premisesImageUrl || [];
      const transformedImages = premisesImageUrls.map(url => 
        typeof url === 'string' ? transformUrl(url) : null
      ).filter(Boolean);
      
      setCaseDetails(result.data);
      setPremisesImages(transformedImages);
      setEvidences(result.data.evidences || []);

      
    } catch (err) {
      console.error('Error fetching case data:', err);
      setError(err.message || 'An unknown error occurred');
    } finally {
      setIsLoading(false);
    }
  }, [caseid, token]);

  useFocusEffect(
    useCallback(() => {
      fetchData();
    }, [fetchData])
  );

  // Helper function to check if evidence has lab info (for selection logic)
  const hasEvidenceLabInfo = useCallback((evidence) => {
    if (evidence.lab_department && evidence.lab_department.length > 0) {
      return true;
    }
    return !!(evidence.labId && evidence.labDepartmentId);
  }, []);

  // Helper function to check if evidence can be sent to forensic
  const canEvidenceBeSentToForensic = useCallback((evidence) => {
    return validationService.canSendToForensic(evidence);
  }, []);

  useEffect(() => {
    setIsSelectionMode(false);
    setSelectedEvidenceIds([]);
  }, [evidences]);

  const handleSelectionConfirm = useCallback(async (apiPayload) => {
    if (!currentEvidenceId) {
      Alert.alert('Error', 'No evidence selected');
      return;
    }

    if (!caseid || !token) {
      Alert.alert('Error', 'Missing case ID or authentication token');
      return;
    }

    if (!apiPayload?.lab_department || apiPayload.lab_department.length === 0) {
      Alert.alert('Error', 'No lab-department selections made');
      return;
    }

    setIsSubmitting(true);

    try {
      const result = await apiService.updateCaseEvidence(token, caseid, currentEvidenceId, apiPayload);

      const labCount = new Set(apiPayload.lab_department.map(item => item.labId)).size;
      const deptCount = apiPayload.lab_department.length;

      Alert.alert('Success!', `The evidence has been successfully shared with ${labCount} lab(s) and ${deptCount} department(s).`);
      setIsModalVisible(false);
      fetchData();
    } catch (err) {
      console.error('Error sharing evidence:', err);
      Alert.alert('Error', err.message || 'Failed to share evidence with labs');
    } finally {
      setIsSubmitting(false);
    }
  }, [currentEvidenceId, caseid, token, fetchData]);

  const handleEvidenceClick = useCallback((evidenceId) => {
    if (isSelectionMode) {
      handleSelectEvidence(evidenceId);
    } else if (evidenceId) {
      router.push({
        pathname: '(screens)/evidenceDetails',
        params: { evidenceId, caseid },
      });
    }
  }, [isSelectionMode, caseid]);

  const handleAddEvidenceClick = useCallback(() => {
    if (!caseid) {
      Alert.alert('Error', 'Missing case ID');
      return;
    }
    
    router.push({
      pathname: '(screens)/captureEvidences',
      params: { caseid },
    });
  }, [caseid]);

  const handleReportClick = useCallback(() => {
    if (typeof changeTab === 'function') {
      changeTab('tab2');
    }
  }, [changeTab]);

  const handleShareButtonClick = useCallback((evidenceId) => {
    if (!evidenceId) {
      Alert.alert('Error', 'No evidence selected');
      return;
    }
    
    setCurrentEvidenceId(evidenceId);
    setIsModalVisible(true);
  }, []);

  const handleLongPress = useCallback((evidenceId, hasLabInfo) => {
    if (!evidenceId) return;

    const evidence = evidences.find(evidence => evidence._id === evidenceId);
    if (!evidence) return;

    if (!hasLabInfo) {
      Alert.alert('Cannot Select', 'Only evidences with lab and department information can be selected.');
      return;
    }

    // Check if evidence can be sent to forensic
    const forensicValidation = canEvidenceBeSentToForensic(evidence);
    if (!forensicValidation.canSend) {
      Alert.alert('Cannot Select', forensicValidation.reason);
      return;
    }

    setIsSelectionMode(true);
    setSelectedEvidenceIds([evidenceId]);
  }, [evidences, canEvidenceBeSentToForensic]);

  const handleSelectEvidence = useCallback((evidenceId) => {
    if (!evidenceId) return;

    const evidence = evidences.find(evidence => evidence._id === evidenceId);
    if (!evidence) return;

    const hasLabInfo = hasEvidenceLabInfo(evidence);

    if (!hasLabInfo) {
      Alert.alert('Cannot Select', 'Only evidences with lab and department information can be selected.');
      return;
    }

    // Check if evidence can be sent to forensic
    const forensicValidation = canEvidenceBeSentToForensic(evidence);
    if (!forensicValidation.canSend) {
      Alert.alert('Cannot Select', forensicValidation.reason);
      return;
    }

    setSelectedEvidenceIds(prevSelected => {
      if (prevSelected.includes(evidenceId)) {
        const newSelected = prevSelected.filter(id => id !== evidenceId);
        if (newSelected.length === 0) {
          setIsSelectionMode(false);
        }
        return newSelected;
      } else {
        return [...prevSelected, evidenceId];
      }
    });
  }, [evidences, hasEvidenceLabInfo, canEvidenceBeSentToForensic]);

  const handleCancelSelection = useCallback(() => {
    setIsSelectionMode(false);
    setSelectedEvidenceIds([]);
  }, []);

  const handleCourtSelectionConfirm = useCallback(async (courtId, courtName, prosecutorId = null, prosecutorName = null) => {
    if (!caseid || !token) {
      Alert.alert('Error', 'Missing case ID or authentication token');
      return;
    }

    setIsSubmitting(true);

    try {
      console.log('Submitting to court with params:', {
        caseid,
        courtId,
        prosecutorId,
        token: token ? 'present' : 'missing'
      });

      const result = await apiService.submitCaseToCourt(token, caseid, courtId, prosecutorId);
      // console.log('Court submission response:', result);

      const successMessage = prosecutorId
        ? `The case has been successfully submitted to ${courtName} with prosecutor ${prosecutorName}.`
        : `The case has been successfully submitted to ${courtName}.`;

      Alert.alert('Success!', successMessage);
      setIsCourtModalVisible(false);
      fetchData();
    } catch (err) {
      console.error('Error submitting case to court:', err);
      console.log('Error details:', {
        message: err.message,
        response: err.response?.data,
        status: err.response?.status,
        headers: err.response?.headers
      });
      Alert.alert('Error', err.message || 'Failed to submit case to court');
    } finally {
      setIsSubmitting(false);
    }
  }, [caseid, token, fetchData]);

  const handleSubmitToJudiciary = useCallback(() => {
    if (isSelectionMode) {
      handleCancelSelection();
    } else {
      setIsCourtModalVisible(true);
    }
  }, [isSelectionMode, handleCancelSelection]);

  const handleProcessSelected = useCallback(async () => {
    if (selectedEvidenceIds.length === 0) {
      Alert.alert('Error', 'No evidences selected');
      return;
    }

    if (!caseid || !token) {
      Alert.alert('Error', 'Missing case ID or authentication token');
      return;
    }

    setIsSubmitting(true);

    try {
      const result = await apiService.submitCaseToForensic(token, caseid, selectedEvidenceIds);
      // console.log('Forensic API response:', JSON.stringify(result, null, 2));
      const qrCode = result?.data?.[0]?.qrCode || null;

      setForensicQr(qrCode);
      setSelectedEvidenceIds([]);
      setIsSelectionMode(false);
      setIsSuccessVisible(true);
    } catch (err) {
      console.error("Error submitting evidence:", err);
      Alert.alert('Error', err.message || 'Failed to submit evidence to forensic');
    } finally {
      setIsSubmitting(false);
    }
  }, [selectedEvidenceIds, caseid, token]);

  const handleAddComment = useCallback((commentText) => {
    if (!commentText.trim()) return;

    const newComment = {
      id: Date.now().toString(),
      text: commentText,
      userName: 'Current User', // You can get this from auth context
      userRole: 'Police Officer', // You can get this from auth context
      timestamp: new Date().toISOString(),
      userAvatar: null, // You can get this from auth context
    };

    setComments(prevComments => [newComment, ...prevComments]);

    // Here you can also make an API call to save the comment
    // await apiService.addComment(token, caseid, commentText);
  }, []);

  // FIR Number editing functions
  const handleEditFirNumber = useCallback(() => {
    setEditedFirNumber(caseDetails?.firNumber || '');
    setIsEditingFir(true);
  }, [caseDetails?.firNumber]);

  const handleCancelFirEdit = useCallback(() => {
    setIsEditingFir(false);
    setEditedFirNumber('');
  }, []);

  const handleSaveFirNumber = useCallback(async () => {
    if (!caseid || !token) {
      Alert.alert('Error', 'Missing case ID or authentication token');
      return;
    }

    setIsSubmitting(true);

    try {
      const updateData = {
        firNumber: editedFirNumber.trim()
      };

      await apiService.updateCaseById(token, caseid, updateData);
      
      Alert.alert('Success!', 'FIR Number updated successfully');
      setIsEditingFir(false);
      setEditedFirNumber('');
      fetchData(); // Refresh the data
    } catch (err) {
      console.error('Error updating FIR number:', err);
      Alert.alert('Error', err.message || 'Failed to update FIR number');
    } finally {
      setIsSubmitting(false);
    }
  }, [caseid, token, editedFirNumber, fetchData]);

  // Location viewer functions
  const parseGPSLocation = useCallback(() => {
    const gpsLocation = caseDetails?.gpsLocation;
    if (!gpsLocation || gpsLocation === 'N/A') return { latitude: null, longitude: null };
    
    // Try to parse different GPS formats like "lat,lng" or "latitude: lat, longitude: lng"
    try {
      // Simple comma-separated format: "28.7041,77.1025"
      if (gpsLocation.includes(',')) {
        const [lat, lng] = gpsLocation.split(',').map(coord => coord.trim());
        if (!isNaN(parseFloat(lat)) && !isNaN(parseFloat(lng))) {
          return { latitude: lat, longitude: lng };
        }
      }
      
      // More complex format: "latitude: 28.7041, longitude: 77.1025"
      const latMatch = gpsLocation.match(/lat(?:itude)?[:\s]*([+-]?\d+\.?\d*)/i);
      const lngMatch = gpsLocation.match(/lon(?:g|gitude)?[:\s]*([+-]?\d+\.?\d*)/i);
      
      if (latMatch && lngMatch) {
        return { latitude: latMatch[1], longitude: lngMatch[1] };
      }
    } catch (error) {
      console.error('Error parsing GPS location:', error);
    }
    
    return { latitude: null, longitude: null };
  }, [caseDetails?.gpsLocation]);

  const handleViewLocation = useCallback(() => {
    setIsLocationViewerVisible(true);
  }, []);

  const handleCloseLocationViewer = useCallback(() => {
    setIsLocationViewerVisible(false);
  }, []);

  // Demo functions
  const handleInfoPress = useCallback(() => {
    setIsDemoVisible(true);
  }, []);

  const handleCloseDemoModal = useCallback(() => {
    setIsDemoVisible(false);
  }, []);

  // const numColumns = width < 600 ? 1 : 3;

  const renderItem = useCallback(({ item }) => {
    switch (item.type) {
      case 'premisesImages':
  
      
        // Check if premisesImages is empty or undefined
        if (!premisesImages || premisesImages.length === 0) {
          return (
            <View style={styles.sectionContainer}>
              <TouchableOpacity
                style={styles.addButton}
                onPress={() =>
                  router.push({
                    pathname: '(screens)/premisesCapture',
                    params: { caseId: caseid },
                  })
                } // Replace with your target route
              >
                <Text style={styles.addButtonText}>Add Premises Image</Text>
              </TouchableOpacity>
            </View>
          );
        }
        return (
          <View style={styles.sectionContainer}>
            <PhotoGrid 
              images={premisesImages}
              description={caseDetails?.premiseDescription || 'No description available'} 
              label='Premises Description'
            />
          </View>
        );


      case 'details':
        return (
          <View style={styles.detailsContainer}>
            <View style={styles.segmentedControlContainer}>
              <View style={styles.segmentedControl}>
                <TouchableOpacity
                  style={[styles.segmentButton, styles.activeSegment]}
                >
                  <MaterialCommunityIcons name="file-document-multiple-outline" size={18} color={Colors.background} />
                  <Text style={styles.activeSegmentText}>Evidences</Text>
                </TouchableOpacity>

                <TouchableOpacity
                  style={styles.segmentButton}
                  onPress={handleReportClick}
                >
                  <MaterialCommunityIcons name="clipboard-text-outline" size={18} color={Colors.lightText} />
                  <Text style={styles.segmentText}>Reports</Text>
                </TouchableOpacity>
              </View>
            </View>
            {[
              { label: 'Created By', value: caseDetails?.createdBy?.name || 'N/A' },
              { label: 'Case Register Date', value: caseDetails?.createdAt ? new Date(caseDetails.createdAt).toLocaleDateString() : 'N/A' },
              { label: 'Case Register Time', value: caseDetails?.createdAt ? new Date(caseDetails.createdAt).toLocaleTimeString() : 'N/A' },
              { label: 'Title', value: caseDetails?.title || 'N/A' },
              { label: 'Description', value: caseDetails?.description || 'N/A' },
              { label: 'Fir Number', value: caseDetails?.firNumber || 'N/A' },
              { label: 'Case Type', value: caseDetails?.caseType || 'N/A' },
              { label: 'Address 1', value: caseDetails?.address1 || 'N/A' },
              { label: 'Address 2', value: caseDetails?.address2 || 'N/A' },
              { label: 'State', value: caseDetails?.state || 'N/A' },
              { label: 'City', value: caseDetails?.city || 'N/A' },
              { label: 'Pincode', value: caseDetails?.pincode || 'N/A' },
              { label: 'GPS Location', value: caseDetails?.gpsLocation || 'N/A' },
              { label: 'Remarks', value: caseDetails?.remarks || 'N/A' },
            ].map((detail, index) => (
              <View style={styles.detailRow} key={index}>
                <Text style={styles.label}>{detail.label}</Text>
                {detail.label === 'Fir Number' && isEditingFir ? (
                  <View style={styles.editContainer}>
                    <TextInput
                      style={styles.editInput}
                      value={editedFirNumber}
                      onChangeText={setEditedFirNumber}
                      placeholder="Enter FIR Number"
                      autoFocus
                    />
                    <View style={styles.editButtons}>
                      <TouchableOpacity
                        style={[styles.editButton, styles.saveButton]}
                        onPress={handleSaveFirNumber}
                        disabled={isSubmitting}
                      >
                        {isSubmitting ? (
                          <ActivityIndicator size="small" color={Colors.background} />
                        ) : (
                          <MaterialCommunityIcons name="check" size={16} color={Colors.background} />
                        )}
                      </TouchableOpacity>
                      <TouchableOpacity
                        style={[styles.editButton, styles.cancelButton]}
                        onPress={handleCancelFirEdit}
                        disabled={isSubmitting}
                      >
                        <MaterialCommunityIcons name="close" size={16} color={Colors.background} />
                      </TouchableOpacity>
                    </View>
                  </View>
                ) : (
                  <Text style={styles.value}>{detail.value}</Text>
                )}
              </View>
            ))}
            
            {/* Action Buttons - Always visible when not editing */}
            {!isEditingFir && (
              <View style={styles.actionButtonsContainer}>
                <TouchableOpacity
                  style={styles.editFirButton}
                  onPress={handleEditFirNumber}
                  disabled={isSubmitting}
                >
                  <MaterialCommunityIcons name="pencil" size={16} color={Colors.primary} />
                  <Text style={styles.editFirButtonText}>Edit FIR Number</Text>
                </TouchableOpacity>
                
                <TouchableOpacity
                  style={styles.viewLocationButton}
                  onPress={handleViewLocation}
                  disabled={isSubmitting}
                >
                  <MaterialCommunityIcons name="map-marker" size={16} color={Colors.primary} />
                  <Text style={styles.viewLocationButtonText}>View Location</Text>
                </TouchableOpacity>
              </View>
            )}
          </View>
        );
      case 'evidences':
        return (
          <EvidencesCards
            evidences={evidences}
            isSelectionMode={isSelectionMode}
            selectedEvidenceIds={selectedEvidenceIds}
            isSubmitting={isSubmitting}
            sectionContainerStyle={styles.sectionContainer}
            sectionTitleStyle={styles.sectionTitle}
            onEvidenceClick={handleEvidenceClick}
            onLongPress={handleLongPress}
            onShareButtonClick={handleShareButtonClick}
            canEvidenceBeSentToForensic={canEvidenceBeSentToForensic}
            onInfoPress={handleInfoPress}
          />
        );
      case 'addButton':
        return (
          <TouchableOpacity
            style={styles.addButton}
            onPress={handleAddEvidenceClick}
            disabled={isSelectionMode || isSubmitting}
          >
            <MaterialCommunityIcons 
              name="plus" 
              size={20} 
              color={(isSelectionMode || isSubmitting) ? Colors.disabled : Colors.primary} 
            />
            <Text style={[
              styles.addButtonText, 
              (isSelectionMode || isSubmitting) && styles.disabledText
            ]}>
              Add New Evidence
            </Text>
          </TouchableOpacity>
        );
      case 'commentSection':
        return (
          <CommentsSection
            comments={comments}
            isSelectionMode={isSelectionMode}
            isSubmitting={isSubmitting}
            styles={styles}
            onAddComment={handleAddComment}
            placeholder="Type your comment here..."
            title="Add Comment"
            commentsTitle="Comments"
            showAddComment={true}
          />
        );
      case 'Buttons':
        return (
          <View style={styles.buttonContainer}>
            <TouchableOpacity
              style={[
                styles.button, 
                isSelectionMode ? { backgroundColor: '#FF3B30' } : styles.button
              ]}
              onPress={handleSubmitToJudiciary}
              disabled={isSubmitting}
            >
              <Text style={styles.buttonText}>
                {isSelectionMode ? 'CANCEL' : 'SUBMIT TO JUDICIARY'}
              </Text>
            </TouchableOpacity>
            <TouchableOpacity
              style={[
                styles.button,
                (selectedEvidenceIds.length === 0 || isSubmitting) && styles.disabledButton
              ]}
              onPress={handleProcessSelected}
              disabled={selectedEvidenceIds.length === 0 || isSubmitting}
            >
              {isSubmitting ? (
                <ActivityIndicator size="small" color={Colors.background} />
              ) : (
                <Text style={styles.buttonText}>
                  SUBMIT TO FORENSIC
                </Text>
              )}
            </TouchableOpacity>
          </View>
        );
      default:
        return null;
    }
  }, [
    premisesImages,
    caseDetails,
    evidences,
    isSelectionMode,
    selectedEvidenceIds,
    isSubmitting,
    comments,
    isEditingFir,
    editedFirNumber,
    isLocationViewerVisible,
    handleReportClick,
    handleAddEvidenceClick,
    handleEvidenceClick,
    handleLongPress,
    handleShareButtonClick,
    handleCancelSelection,
    handleProcessSelected,
    handleAddComment,
    handleEditFirNumber,
    handleCancelFirEdit,
    handleSaveFirNumber,
    handleViewLocation,
    parseGPSLocation,
    canEvidenceBeSentToForensic,
    handleInfoPress
  ]);

  // Memoized data list to prevent unnecessary re-renders
  const data = useMemo(() => [
    { type: 'premisesImages' },
    { type: 'details' },
    { type: 'evidences' },
    { type: 'addButton' },
    { type: 'commentSection' },
    { type: 'Buttons' },
  ], []);

  // Error display
  if (error) {
    return (
      <View style={styles.errorContainer}>
        <MaterialCommunityIcons name="alert-circle-outline" size={48} color="#FF3B30" />
        <Text style={styles.errorText}>Error: {error}</Text>
        <TouchableOpacity style={styles.retryButton} onPress={fetchData}>
          <Text style={styles.retryButtonText}>Retry</Text>
        </TouchableOpacity>
      </View>
    );
  }

  // Full-screen loader
  if (isLoading) {
    return (
      <View style={styles.fullScreenLoader}>
        <ActivityIndicator size="large" color={Colors.primary} />
      </View>
    );
  }

  return (
    <GestureHandlerRootView style={{ flex: 1 }}>
      <View style={styles.container}>
        <FlatList
          data={data}
          renderItem={renderItem}
          keyExtractor={(item, index) => `${item.type}-${index}`}
          contentContainerStyle={{ flexGrow: 1 }}
          initialNumToRender={3}
          maxToRenderPerBatch={5}
          windowSize={5}
        />
      </View>

      <LabModal
        isVisible={isModalVisible}
        onClose={() => setIsModalVisible(false)}
        onConfirmSelection={handleSelectionConfirm}
        isSubmitting={isSubmitting}
      />

      <CourtModal
        isVisible={isCourtModalVisible}
        onClose={() => setIsCourtModalVisible(false)}
        onConfirmSelection={handleCourtSelectionConfirm}
        isSubmitting={isSubmitting}
      />

      {isSuccessVisible && (
        <SuccessScreen
          message="Evidence data synced successfully!"
          duration={2000}
          onComplete={() => {
            setIsSuccessVisible(false);
            if (forensicQr) {
              router.replace({
                pathname: '(screens)/forensicQr',
                params: {
                  caseid: caseid,
                  ForensicQr: forensicQr,
                },
                resetHistory: true
              });
            } else {
              fetchData();
            }
          }}
        />
      )}

      <LocationViewer
        isVisible={isLocationViewerVisible}
        onClose={handleCloseLocationViewer}
        latitude={parseGPSLocation().latitude}
        longitude={parseGPSLocation().longitude}
        title="Case Location"
      />

      <EvidenceProcessDemo
        isVisible={isDemoVisible}
        onClose={handleCloseDemoModal}
      />
    </GestureHandlerRootView>
  );
};

export default React.memo(Tab1);

const styles = StyleSheet.create({
  container: {
    flex: 1,
    backgroundColor: Colors.background,
  },
  detailsContainer: {
    paddingHorizontal: '2%',
    paddingVertical: 16,
  },
  detailRow: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    paddingVertical: 8,
  },
  label: {
    fontSize: 14,
    opacity: 0.5,
    width: '40%',
    fontFamily: 'Roboto',
  },
  value: {
    fontSize: 14,
    width: '60%',
    fontWeight: '450',
    fontFamily: 'Roboto',
  },
  addButton: {
    flexDirection: 'row',
    alignItems: 'center',
    padding: 16,
    borderRadius: 8,
    marginTop: 4,
    alignSelf: 'flex-end',
  },
  addButtonText: {
    color: Colors.primary,
    fontSize: 18,
    marginLeft: 8,
    fontWeight: 'bold',
    fontFamily: 'Roboto_Bold',
  },
  disabledText: {
    color: Colors.disabled,
    fontFamily: 'Roboto',
  },
  sectionContainer: {
    marginTop: 20,
  },
  sectionTitle: {
    textAlign: 'left',
    color: Colors.primary,
    fontWeight: '500',
    fontSize: 18,
    marginBottom: 14,
    marginHorizontal: '3%',
    fontFamily: 'Roboto_bold',
  },

  
  errorContainer: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
    backgroundColor: Colors.background,
  },
  errorText: {
    color: '#FF3B30',
    marginVertical: 16,
    textAlign: 'center',
    fontSize: 16,
    fontFamily: 'Roboto',
  },
  retryButton: {
    backgroundColor: Colors.primary,
    paddingVertical: 10,
    paddingHorizontal: 20,
    borderRadius: 8,
  },
  retryButtonText: {
    color: Colors.background,
    fontSize: 16,
    fontWeight: '500',
  },
  commentSection: {
    marginHorizontal: '2%',
    marginTop: 0,
  },
  commentInput: {
    borderWidth: 1,
    borderColor: Colors.border,
    borderRadius: 8,
    padding: 12,
    minHeight: 100,
    marginBottom: 16,
    fontSize: 14,
  },
  commentButton: {
    backgroundColor: Colors.primary,
    padding: 16,
    borderRadius: 50,
    alignItems: 'center',
    marginBottom: 16,
  },
  disabledButton: {
    backgroundColor: Colors.disabled,
    opacity: 0.7,
  },
  commentButtonText: {
    color: Colors.background,
    fontSize: 16,
    fontWeight: '500',
  },
  commentsDisplay: {
    marginTop: 16,
  },
  commentsTitle: {
    fontSize: 16,
    fontWeight: '600',
    marginBottom: 12,
  },
  commentItem: {
    flexDirection: 'row',
    alignItems: 'flex-start',
    marginBottom: 16,
  },
  userAvatar: {
    width: 40,
    height: 40,
    borderRadius: 20,
    marginRight: 12,
    backgroundColor: '#f0f0f0',
  },
  commentContent: {
    flex: 1,
  },
  userName: {
    fontSize: 14,
    fontWeight: '500',
    marginBottom: 4,
    fontFamily: 'Roboto',
  },
  commentText: {
    fontSize: 14,
    color: Colors.black,
    fontFamily: 'Roboto',
  },
  imageGrid: {
    justifyContent: 'center',
    alignItems: 'center',
  },
  image: {
    width: 100,
    height: 100,
    margin: 5,
    borderRadius: 8,
  },
  fullScreenLoader: {
    flex: 1,
    justifyContent: 'center',
    alignItems: 'center',
    backgroundColor: Colors.background,
  },
  segmentedControlContainer: {
    alignItems: 'flex-start',
    marginBottom: 24,
  },
  segmentedControl: {
    flexDirection: 'row',
    borderRadius: 30,
    overflow: 'hidden',
    borderWidth: 1,
    borderColor: Colors.border,
    backgroundColor: '#E8E8E8',
  },
  segmentButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 10,
    paddingHorizontal: 20,
    justifyContent: 'center',
    width: 150,
  },
  activeSegment: {
    backgroundColor: '#367E18',
    borderRadius: 30,
  },
  segmentText: {
    color: '#666666',
    marginLeft: 8,
    fontWeight: '500',
    fontFamily: 'Roboto',
  },
  activeSegmentText: {
    color: '#FFFFFF',
    marginLeft: 8,
    fontWeight: '500',
    fontFamily: 'Roboto',
  },

  buttonContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    marginHorizontal: '2%',
    marginTop: 16,
    marginBottom: 24,
  },
  button: {
    flex: 1,
    backgroundColor: '#0B36A1',
    padding: 16,
    borderRadius: 50,
    alignItems: 'center',
    justifyContent: 'center',
    marginHorizontal: 4,
  },
  buttonText: {
    color: '#FFFFFF',
    fontSize: 12,
    fontWeight: '500',
    fontFamily: 'Roboto',
  },
  
  // FIR Edit Styles
  editContainer: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
    width: '60%',
  },
  editInput: {
    flex: 1,
    borderWidth: 1,
    borderColor: Colors.border,
    borderRadius: 6,
    paddingHorizontal: 12,
    paddingVertical: 8,
    fontSize: 14,
    fontFamily: 'Roboto',
    marginRight: 8,
  },
  editButtons: {
    flexDirection: 'row',
    gap: 4,
  },
  editButton: {
    width: 28,
    height: 28,
    borderRadius: 14,
    justifyContent: 'center',
    alignItems: 'center',
  },
  saveButton: {
    backgroundColor: '#34C759',
  },
  cancelButton: {
    backgroundColor: '#FF3B30',
  },
  actionButtonsContainer: {
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    marginTop: 16,
    gap: 12,
  },
  editFirButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 12,
    paddingHorizontal: 24,
    borderRadius: 50,
    borderWidth: 1,
    borderColor: Colors.primary,
    backgroundColor: 'transparent',
    minWidth: 160,
  },
  editFirButtonText: {
    color: Colors.primary,
    fontSize: 14,
    marginLeft: 6,
    fontFamily: 'Roboto',
    fontWeight: '500',
  },
  viewLocationButton: {
    flexDirection: 'row',
    alignItems: 'center',
    justifyContent: 'center',
    paddingVertical: 12,
    paddingHorizontal: 24,
    borderRadius: 50,
    borderWidth: 1,
    borderColor: Colors.primary,
    backgroundColor: 'transparent',
    minWidth: 160,
  },
  viewLocationButtonText: {
    color: Colors.primary,
    fontSize: 14,
    marginLeft: 6,
    fontFamily: 'Roboto',
    fontWeight: '500',
  },
});