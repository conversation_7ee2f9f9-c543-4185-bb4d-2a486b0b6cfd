import React, { useCallback, useState, useEffect } from 'react';
import { View, Text, TouchableOpacity, Image, StyleSheet } from 'react-native';
import MaterialCommunityIcons from '@expo/vector-icons/MaterialCommunityIcons';
import { Colors } from '../../constants/colors';
import { transformUrl } from '../../utils/transformUrl';
import { getForensicStatus } from '../../services/validation';
import Badge from '../Common/Badge';
import getCurrentTime from '../../utils/currentTime';

const TimeRemaining = ({ createdAt, durationInMinutes, forensicRequests }) => {
  const [timeInfo, setTimeInfo] = useState({ text: '', icon: 'clock-outline', color: Colors.lightText });

  useEffect(() => {
    const calculateTimeInfo = () => {
      // Get the latest forensic request
      const latestRequest = forensicRequests?.[0];
      
      if (latestRequest) {
        switch (latestRequest.status) {
          case 'dispatched':
            const dispatchTime = new Date(latestRequest.createdAt);
            const createdTime = new Date(createdAt);
            const timeToSubmit = dispatchTime - createdTime;
            const minutesToSubmit = Math.floor(timeToSubmit / (1000 * 60));
            
            if (minutesToSubmit <= durationInMinutes) {
              setTimeInfo({
                text: 'Submitted on time',
                icon: 'check-circle-outline',
                color: '#4CAF50'
              });
            } else {
              setTimeInfo({
                text: 'Submitted late',
                icon: 'alert-circle-outline',
                color: '#FFA000'
              });
            }
            break;

          case 'pending':
            setTimeInfo({
              text: 'Pending dispatch',
              icon: 'clock-outline',
              color: '#2196F3'
            });
            break;

          case 'processing':
            setTimeInfo({
              text: 'Processing',
              icon: 'cog-outline',
              color: '#9C27B0'
            });
            break;

          case 'completed':
            setTimeInfo({
              text: 'Report Generated',
              icon: 'check-circle',
              color: '#4CAF50'
            });
            break;

          case 'rejected':
            setTimeInfo({
              text: 'Rejected',
              icon: 'close-circle-outline',
              color: '#F44336'
            });
            break;

          default:
            // For any other status or no status, show remaining time
            const createdDate = new Date(createdAt);
            const deadlineDate = new Date(createdDate.getTime() + durationInMinutes * 60000);
            const now = new Date(getCurrentTime.now());
            const diff = deadlineDate - now;
            
            if (diff <= 0) {
              setTimeInfo({
                text: 'Time expired',
                icon: 'clock-alert-outline',
                color: '#F44336'
              });
            } else {
              const hours = Math.floor(diff / (1000 * 60 * 60));
              const minutes = Math.floor((diff % (1000 * 60 * 60)) / (1000 * 60));
              
              setTimeInfo({
                text: hours > 0 ? `${hours}h ${minutes}m` : `${minutes}m`,
                icon: 'clock-outline',
                color: Colors.lightText
              });
            }
        }
      } else {
        // No forensic requests, show remaining time
        const createdDate = new Date(createdAt);
        const deadlineDate = new Date(createdDate.getTime() + durationInMinutes * 60000);
        const now = new Date(getCurrentTime.now());
        const diff = deadlineDate - now;
        
        if (diff <= 0) {
          setTimeInfo({
            text: 'Time expired',
            icon: 'clock-alert-outline',
            color: '#F44336'
          });
        } else {
          const hours = Math.floor(diff / (1000 * 60 * 60));
          const minutes = Math.floor((diff % (1000 * 60 * 60)) / (1000 * 60));
          
          setTimeInfo({
            text: hours > 0 ? `${hours}h ${minutes}m` : `${minutes}m`,
            icon: 'clock-outline',
            color: Colors.lightText
          });
        }
      }
    };

    calculateTimeInfo();
    const interval = setInterval(calculateTimeInfo, 60000); // Update every minute

    return () => clearInterval(interval);
  }, [createdAt, durationInMinutes, forensicRequests]);

  return (
    <View style={styles.timeContainer}>
      <MaterialCommunityIcons 
        name={timeInfo.icon}
        size={14} 
        color={timeInfo.color}
      />
      <Text style={[styles.timeText, { color: timeInfo.color }]}>
        {timeInfo.text}
      </Text>
    </View>
  );
};

const EvidencesCards = ({
  evidences,
  isSelectionMode,
  selectedEvidenceIds,
  isSubmitting,
  sectionContainerStyle,
  sectionTitleStyle,
  onEvidenceClick,
  onLongPress,
  onShareButtonClick,
  canEvidenceBeSentToForensic,
}) => {
  // Helper function to get lab and department names from evidence data
  const getLabsAndDepartmentsFromEvidence = useCallback((evidence) => {
    // First check if evidence has lab_department array with populated data
    if (evidence.lab_department && evidence.lab_department.length > 0) {
      // Sort by priority (ascending: 1, 2, 3...) and get unique labs and departments
      const sortedLabDept = evidence.lab_department.sort((a, b) => a.priority - b.priority);
      
      // Get unique labs (by priority)
      const uniqueLabs = [];
      const seenLabIds = new Set();
      sortedLabDept.forEach(item => {
        if (!seenLabIds.has(item.labId._id)) {
          uniqueLabs.push(item.labId.name);
          seenLabIds.add(item.labId._id);
        }
      });
      
      // Get unique departments (by priority)
      const uniqueDepartments = [];
      const seenDeptIds = new Set();
      sortedLabDept.forEach(item => {
        if (!seenDeptIds.has(item.labDepartmentId._id)) {
          uniqueDepartments.push(item.labDepartmentId.name);
          seenDeptIds.add(item.labDepartmentId._id);
        }
      });
      
      return {
        labName: uniqueLabs.join(', '),
        departmentName: uniqueDepartments.join(', '),
        hasLabInfo: true
      };
    }
    
    // Fallback to old method for backward compatibility
    if (evidence.labId && evidence.labDepartmentId) {
      const labName = typeof evidence.labId === 'object' && evidence.labId?.name
        ? evidence.labId.name
        : 'Unknown Lab';

      const departmentName = typeof evidence.labDepartmentId === 'object' && evidence.labDepartmentId?.name
        ? evidence.labDepartmentId.name
        : 'Unknown Department';

      return {
        labName,
        departmentName,
        hasLabInfo: true
      };
    }
    
    return {
      labName: null,
      departmentName: null,
      hasLabInfo: false
    };
  }, []);

  const renderEvidenceMedia = useCallback((evidence) => {
    const defaultImage = require('../../assets/images/small_satya_smadhanLogo.png');
    const url = evidence.attachmentUrl?.[0];
    
    if (!url || url.trim() === '') {
      return (
        <View style={styles.mediaContainer}>
          <Image
            source={defaultImage}
            style={styles.media}
            resizeMode="contain"
          />
        </View>
      );
    }

    return (
      <View style={styles.mediaContainer}>
        <Image
          source={{ uri: transformUrl(url) }}
          style={styles.media}
          resizeMode="cover"
          defaultSource={defaultImage}
          onError={(error) => {
            console.log('Image loading error:', error.nativeEvent.error);
            return (
              <View style={styles.mediaContainer}>
                <Image
                  source={defaultImage}
                  style={styles.media}
                  resizeMode="contain"
                />
              </View>
            );
          }}
        />
      </View>
    );
  }, []);

  const renderEvidenceTags = useCallback((evidence) => {
    if (!evidence.tags || evidence.tags.length === 0) return null;

    return (
      <View style={styles.tagsContainer}>
        {evidence.tags.map((tag, index) => (
          <View key={index} style={styles.tagRow}>
            <Badge
              label={tag.type}
              style={styles.tagBadge}
              textStyle={styles.tagText}
            />
            <TimeRemaining 
              createdAt={evidence.createdAt}
              durationInMinutes={tag.durationInMinutesToSubmit}
              forensicRequests={evidence.forensicRequests}
            />
          </View>
        ))}
      </View>
    );
  }, []);

  return (
    <View style={sectionContainerStyle}>
      <Text style={sectionTitleStyle}>
        {isSelectionMode
          ? `Evidences (${selectedEvidenceIds.length} selected)`
          : 'Evidences'}
      </Text>
      {evidences && evidences.length > 0 ? (
        evidences.map((evidence, index) => {
          if (!evidence) return null;

          const firstAttachment = evidence.attachmentUrl?.[0] || null;
          const labDeptInfo = getLabsAndDepartmentsFromEvidence(evidence);
          const hasLabInfo = labDeptInfo.hasLabInfo;
          const isSelected = selectedEvidenceIds.includes(evidence._id);
          const durationInMinutes = evidence.tags?.[0]?.durationInMinutesToSubmit || 0;

          // Check forensic status if function is provided
          const forensicValidation = canEvidenceBeSentToForensic ? canEvidenceBeSentToForensic(evidence) : { canSend: true };
          const canSendToForensic = forensicValidation.canSend;

          // Get detailed forensic status for display
          const forensicStatusInfo = getForensicStatus(evidence);

          return (
            <TouchableOpacity
              key={evidence._id || index}
              style={[
                styles.evidenceCard,
                isSelected && styles.selectedEvidenceCard
              ]}
              onPress={() => onEvidenceClick(evidence._id)}
              onLongPress={() => onLongPress(evidence._id, hasLabInfo)}
              delayLongPress={300}
              disabled={isSubmitting}
            >
              {isSelected && (
                <View style={styles.selectedIndicator}>
                  <MaterialCommunityIcons name="check-circle" size={24} color={Colors.primary} />
                </View>
              )}
              {durationInMinutes > 0 && (
                <TimeRemaining 
                  createdAt={evidence.createdAt} 
                  durationInMinutes={durationInMinutes}
                  forensicRequests={evidence.forensicRequests}
                />
              )}
              <View style={styles.imageContainer}>
                {renderEvidenceMedia(evidence)}
              </View>
              <View style={styles.evidenceDetails}>
                <Text style={styles.evidenceTitle}>{evidence.title || 'Untitled Evidence'}</Text>
                
                <View style={styles.detailRow}>
                  <Text style={styles.evidenceLabel}>Type:</Text>
                  <Text style={styles.evidenceType}>{evidence.type || 'Unknown'}</Text>
                </View>

                {labDeptInfo.labName && (
                  <View style={styles.detailRow}>
                    <Text style={styles.evidenceLabel}>Lab:</Text>
                    <Text style={styles.evidenceType}>{labDeptInfo.labName}</Text>
                  </View>
                )}
                
                {labDeptInfo.departmentName && (
                  <View style={styles.detailRow}>
                    <Text style={styles.evidenceLabel}>Department:</Text>
                    <Text style={styles.evidenceType}>{labDeptInfo.departmentName}</Text>
                  </View>
                )}

                {hasLabInfo && (
                  <View style={styles.detailRow}>
                    <Text style={styles.evidenceLabel}>Status:</Text>
                    <Badge 
                      text={forensicStatusInfo.displayName}
                      type={canSendToForensic ? 'success' : 'warning'}
                      size="small"
                      style={styles.statusBadge}
                    />
                  </View>
                )}
              </View>
              {!isSelectionMode && (
                <TouchableOpacity
                  style={styles.shareButton}
                  onPress={() => onShareButtonClick(evidence._id)}
                  disabled={isSubmitting}
                >
                  <MaterialCommunityIcons
                    name="share-circle"
                    size={24}
                    color={hasLabInfo && canSendToForensic ? "#4CAF50" : hasLabInfo ? "#FF9800" : Colors.primary}
                  />
                </TouchableOpacity>
              )}
            </TouchableOpacity>
          );
        })
      ) : (
        <Text style={styles.noEvidenceText}>No evidences found</Text>
      )}
    </View>
  );
};

const styles = StyleSheet.create({
  evidenceCard: {
    borderColor: Colors.border,
    borderWidth: 1,
    marginHorizontal: '2%',
    marginBottom: 24,
    padding: 12,
    borderRadius: 10,
    flexDirection: 'row',
    position: 'relative',
  },
  selectedEvidenceCard: {
    borderColor: Colors.primary,
    borderWidth: 2,
    backgroundColor: 'rgba(11, 54, 161, 0.05)',
  },
  selectedIndicator: {
    position: 'absolute',
    top: 8,
    right: 8,
    zIndex: 1,
  },
  imageContainer: {
    width: 80,
    marginRight: 12,
    justifyContent: 'center',
    alignItems: 'center',
  },
  evidenceImage: {
    height: 80,
    width: 80,
    borderRadius: 8,
    backgroundColor: '#f0f0f0',
    resizeMode: 'cover',
  },
  evidenceDetails: {
    flex: 1,
    gap: 6,
  },
  detailRow: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 4,
  },
  evidenceLabel: {
    fontSize: 12,
    color: Colors.lightText,
    width: 80,
  },
  evidenceType: {
    fontSize: 12,
    color: Colors.lightText,
    flex: 1,
  },
  evidenceTitle: {
    fontSize: 15,
    color: Colors.black,
    marginBottom: 8,
  },
  shareButton: {
    padding: 4,
    justifyContent: 'center',
    alignItems: 'center',
  },
  noEvidenceText: {
    textAlign: 'center',
    color: '#666',
    fontStyle: 'italic',
    marginTop: 20,
    marginBottom: 40,
    fontFamily: 'Roboto',
  },
  playIcon: {
    position: 'absolute',
    top: '50%',
    left: '50%',
    transform: [{ translateX: -15 }, { translateY: -15 }],
    zIndex: 1,
  },
  videoOverlay: {
    position: 'absolute',
    top: 0,
    left: 0,
    right: 0,
    bottom: 0,
    backgroundColor: 'rgba(0, 0, 0, 0.4)',
    justifyContent: 'center',
    alignItems: 'center',
    borderRadius: 8,
  },
  videoLabel: {
    color: '#FFFFFF',
    fontWeight: 'bold',
    fontSize: 12,
    backgroundColor: 'rgba(0, 0, 0, 0.6)',
    paddingHorizontal: 6,
    paddingVertical: 3,
    borderRadius: 4,
    fontFamily: 'Roboto',
  },
  statusContainer: {
    marginTop: 2,
  },
  statusBadge: {
    opacity: 0.9,
  },
  timeContainer: {
    position: 'absolute',
    top: 8,
    right: 8,
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: 'rgba(255, 255, 255, 0.9)',
    paddingHorizontal: 8,
    paddingVertical: 4,
    borderRadius: 12,
    zIndex: 2,
  },
  timeText: {
    fontSize: 12,
    color: Colors.lightText,
    marginLeft: 4,
    fontFamily: 'Roboto',
  },
  expiredTime: {
    color: '#F44336',
  },
  mediaContainer: {
    width: 80,
    height: 80,
    borderRadius: 8,
    backgroundColor: '#f0f0f0',
    justifyContent: 'center',
    alignItems: 'center',
  },
  media: {
    width: '100%',
    height: '100%',
    borderRadius: 8,
  },
  tagsContainer: {
    marginTop: 8,
  },
  tagRow: {
    flexDirection: 'row',
    alignItems: 'center',
    marginBottom: 4,
  },
  tagBadge: {
    marginRight: 8,
  },
  tagText: {
    fontSize: 12,
    color: Colors.lightText,
  },
});

export default EvidencesCards;
