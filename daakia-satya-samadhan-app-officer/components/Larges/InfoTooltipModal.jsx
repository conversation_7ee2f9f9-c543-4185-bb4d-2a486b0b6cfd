import React, { useState, useRef } from 'react';
import {
  View,
  Text,
  Modal,
  TouchableOpacity,
  ScrollView,
  StyleSheet,
  Animated,
  Dimensions,
} from 'react-native';
import MaterialCommunityIcons from '@expo/vector-icons/MaterialCommunityIcons';
import { Colors } from '../../constants/colors';

const { width: screenWidth, height: screenHeight } = Dimensions.get('window');

const InfoTooltipModal = ({ isVisible, onClose }) => {
  const [currentStep, setCurrentStep] = useState(0);
  const fadeAnim = useRef(new Animated.Value(0)).current;
  const scaleAnim = useRef(new Animated.Value(0.8)).current;

  const steps = [
    {
      title: "How to Send Evidence to Forensic Labs or Courts",
      icon: "information-outline",
      content: "This guide will show you the complete process of sending evidence for forensic analysis or court proceedings.",
      color: Colors.primary
    },
    {
      title: "Step 1: Select Evidence",
      icon: "checkbox-multiple-marked-outline",
      content: "Long press on any evidence card to enter selection mode. You can then tap multiple evidence items to select them for sending.\n\n📱 Demo: Try long-pressing on evidence cards below to see selection mode in action.",
      color: "#4CAF50"
    },
    {
      title: "Step 2: Choose Destination",
      icon: "share-circle",
      content: "After selecting evidence, tap the share button (🔗) on the evidence card. You'll see options to send to either:\n\n🧪 Forensic Labs - For scientific analysis\n⚖️ Courts - For legal proceedings",
      color: "#FF9800"
    },
    {
      title: "Step 3: Lab Selection (If Forensic)",
      icon: "flask-outline",
      content: "If sending to forensic labs:\n\n1️⃣ Select the appropriate lab from the list\n2️⃣ Choose specific departments (DNA, Ballistics, etc.)\n3️⃣ Set priority order by dragging items\n4️⃣ Confirm your selection\n\n⚡ Tip: You can select multiple labs and departments for comprehensive analysis.",
      color: "#9C27B0"
    },
    {
      title: "Step 4: Court Selection (If Court)",
      icon: "gavel",
      content: "If sending to court:\n\n1️⃣ Select the appropriate court from available options\n2️⃣ Optionally choose a specific prosecutor\n3️⃣ Review court details and location\n4️⃣ Submit your evidence transfer request\n\n📋 Note: Prosecutor selection is optional but recommended for faster processing.",
      color: "#F44336"
    },
    {
      title: "Step 5: Track Progress",
      icon: "clock-check-outline",
      content: "After submission, you can track your evidence:\n\n📊 Status Updates:\n• Pending - Awaiting dispatch\n• Processing - Under analysis\n• Completed - Report ready\n\n⏰ Time Tracking:\n• Monitor submission deadlines\n• View processing time\n• Get completion notifications\n\n📄 Reports:\n• Download forensic reports\n• View court submission confirmations\n• Access case documentation",
      color: "#2196F3"
    }
  ];

  React.useEffect(() => {
    if (isVisible) {
      setCurrentStep(0);
      Animated.parallel([
        Animated.timing(fadeAnim, {
          toValue: 1,
          duration: 300,
          useNativeDriver: true,
        }),
        Animated.spring(scaleAnim, {
          toValue: 1,
          tension: 100,
          friction: 8,
          useNativeDriver: true,
        }),
      ]).start();
    } else {
      Animated.parallel([
        Animated.timing(fadeAnim, {
          toValue: 0,
          duration: 200,
          useNativeDriver: true,
        }),
        Animated.timing(scaleAnim, {
          toValue: 0.8,
          duration: 200,
          useNativeDriver: true,
        }),
      ]).start();
    }
  }, [isVisible]);

  const handleNext = () => {
    if (currentStep < steps.length - 1) {
      setCurrentStep(currentStep + 1);
    }
  };

  const handlePrevious = () => {
    if (currentStep > 0) {
      setCurrentStep(currentStep - 1);
    }
  };

  const handleClose = () => {
    onClose();
  };

  const currentStepData = steps[currentStep];

  return (
    <Modal
      visible={isVisible}
      transparent={true}
      animationType="none"
      onRequestClose={handleClose}
    >
      <View style={styles.overlay}>
        <Animated.View
          style={[
            styles.modalContainer,
            {
              opacity: fadeAnim,
              transform: [{ scale: scaleAnim }],
            },
          ]}
        >
          {/* Header */}
          <View style={styles.header}>
            <View style={styles.headerLeft}>
              <View style={[styles.iconContainer, { backgroundColor: currentStepData.color + '20' }]}>
                <MaterialCommunityIcons
                  name={currentStepData.icon}
                  size={24}
                  color={currentStepData.color}
                />
              </View>
              <Text style={styles.title}>{currentStepData.title}</Text>
            </View>
            <TouchableOpacity onPress={handleClose} style={styles.closeButton}>
              <MaterialCommunityIcons name="close" size={24} color={Colors.lightText} />
            </TouchableOpacity>
          </View>

          {/* Progress Indicator */}
          <View style={styles.progressContainer}>
            {steps.map((_, index) => (
              <View
                key={index}
                style={[
                  styles.progressDot,
                  {
                    backgroundColor: index <= currentStep ? currentStepData.color : Colors.border,
                  },
                ]}
              />
            ))}
          </View>

          {/* Content */}
          <ScrollView style={styles.contentContainer} showsVerticalScrollIndicator={false}>
            <Text style={styles.content}>{currentStepData.content}</Text>
            
            {currentStep === 0 && (
              <View style={styles.demoContainer}>
                <Text style={styles.demoText}>
                  💡 This is an interactive demo guide showing you how to efficiently send evidence for analysis or court proceedings.
                </Text>
                <View style={styles.featureList}>
                  <Text style={styles.featureItem}>✅ Step-by-step guidance</Text>
                  <Text style={styles.featureItem}>✅ Real-time status tracking</Text>
                  <Text style={styles.featureItem}>✅ Multiple destination options</Text>
                  <Text style={styles.featureItem}>✅ Priority management</Text>
                </View>
              </View>
            )}

            {currentStep === 1 && (
              <View style={styles.demoContainer}>
                <Text style={styles.demoText}>
                  🎯 Pro Tip: You can select multiple evidence items at once by tapping each one after entering selection mode.
                </Text>
              </View>
            )}

            {currentStep === 2 && (
              <View style={styles.demoContainer}>
                <Text style={styles.demoText}>
                  🔄 Quick Access: The share button color indicates the evidence status - green for ready, orange for pending setup.
                </Text>
              </View>
            )}
          </ScrollView>

          {/* Navigation */}
          <View style={styles.navigationContainer}>
            <TouchableOpacity
              style={[
                styles.navButton,
                styles.previousButton,
                currentStep === 0 && styles.disabledButton,
              ]}
              onPress={handlePrevious}
              disabled={currentStep === 0}
            >
              <MaterialCommunityIcons
                name="chevron-left"
                size={20}
                color={currentStep === 0 ? Colors.disabled : Colors.lightText}
              />
              <Text
                style={[
                  styles.navButtonText,
                  currentStep === 0 && styles.disabledText,
                ]}
              >
                Previous
              </Text>
            </TouchableOpacity>

            <Text style={styles.stepIndicator}>
              {currentStep + 1} of {steps.length}
            </Text>

            {currentStep < steps.length - 1 ? (
              <TouchableOpacity
                style={[styles.navButton, styles.nextButton]}
                onPress={handleNext}
              >
                <Text style={styles.navButtonText}>Next</Text>
                <MaterialCommunityIcons
                  name="chevron-right"
                  size={20}
                  color={Colors.background}
                />
              </TouchableOpacity>
            ) : (
              <TouchableOpacity
                style={[styles.navButton, styles.finishButton]}
                onPress={handleClose}
              >
                <Text style={styles.navButtonText}>Got it!</Text>
                <MaterialCommunityIcons
                  name="check"
                  size={20}
                  color={Colors.background}
                />
              </TouchableOpacity>
            )}
          </View>
        </Animated.View>
      </View>
    </Modal>
  );
};

const styles = StyleSheet.create({
  overlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.6)',
    justifyContent: 'center',
    alignItems: 'center',
    padding: 20,
  },
  modalContainer: {
    backgroundColor: Colors.background,
    borderRadius: 16,
    width: '100%',
    maxWidth: 400,
    maxHeight: screenHeight * 0.8,
    elevation: 10,
    shadowColor: '#000',
    shadowOffset: { width: 0, height: 4 },
    shadowOpacity: 0.3,
    shadowRadius: 8,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'flex-start',
    padding: 20,
    paddingBottom: 16,
    borderBottomWidth: 1,
    borderBottomColor: Colors.border,
  },
  headerLeft: {
    flex: 1,
    flexDirection: 'row',
    alignItems: 'center',
  },
  iconContainer: {
    width: 40,
    height: 40,
    borderRadius: 20,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
  },
  title: {
    fontSize: 16,
    fontWeight: 'bold',
    color: Colors.black,
    fontFamily: 'Roboto_Bold',
    flex: 1,
    flexWrap: 'wrap',
  },
  closeButton: {
    padding: 4,
  },
  progressContainer: {
    flexDirection: 'row',
    justifyContent: 'center',
    alignItems: 'center',
    paddingHorizontal: 20,
    paddingVertical: 16,
    gap: 8,
  },
  progressDot: {
    width: 8,
    height: 8,
    borderRadius: 4,
  },
  contentContainer: {
    flex: 1,
    paddingHorizontal: 20,
  },
  content: {
    fontSize: 14,
    lineHeight: 22,
    color: Colors.black,
    fontFamily: 'Roboto',
    textAlign: 'left',
  },
  demoContainer: {
    backgroundColor: '#FFF3CD',
    borderRadius: 8,
    padding: 12,
    marginTop: 16,
    borderLeftWidth: 4,
    borderLeftColor: '#FFC107',
  },
  demoText: {
    fontSize: 13,
    color: '#856404',
    fontFamily: 'Roboto',
    fontStyle: 'italic',
    marginBottom: 8,
  },
  featureList: {
    marginTop: 8,
  },
  featureItem: {
    fontSize: 12,
    color: '#856404',
    fontFamily: 'Roboto',
    marginBottom: 4,
  },
  navigationContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 20,
    borderTopWidth: 1,
    borderTopColor: Colors.border,
  },
  navButton: {
    flexDirection: 'row',
    alignItems: 'center',
    paddingVertical: 8,
    paddingHorizontal: 16,
    borderRadius: 20,
    gap: 4,
  },
  previousButton: {
    backgroundColor: Colors.lightText,
  },
  nextButton: {
    backgroundColor: Colors.primary,
  },
  finishButton: {
    backgroundColor: '#4CAF50',
  },
  disabledButton: {
    backgroundColor: Colors.disabled,
  },
  navButtonText: {
    fontSize: 14,
    fontWeight: '500',
    color: Colors.background,
    fontFamily: 'Roboto',
  },
  disabledText: {
    color: Colors.disabled,
  },
  stepIndicator: {
    fontSize: 12,
    color: Colors.lightText,
    fontFamily: 'Roboto',
  },
});

export default InfoTooltipModal;
