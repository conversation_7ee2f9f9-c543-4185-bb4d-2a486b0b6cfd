import React, { useState, useEffect, useRef } from 'react';
import {
  View,
  Text,
  TouchableOpacity,
  Modal,
  StyleSheet,
  Animated,
  Dimensions,
  ScrollView,
} from 'react-native';
import MaterialCommunityIcons from '@expo/vector-icons/MaterialCommunityIcons';
import { Colors } from '../../constants/colors';

const { width, height } = Dimensions.get('window');

const EvidenceProcessDemo = ({ isVisible, onClose }) => {
  const [currentStep, setCurrentStep] = useState(0);
  const [showTooltip, setShowTooltip] = useState(false);
  
  // Animation values
  const fadeAnim = useRef(new Animated.Value(0)).current;
  const slideAnim = useRef(new Animated.Value(50)).current;
  const scaleAnim = useRef(new Animated.Value(0.8)).current;
  const pulseAnim = useRef(new Animated.Value(1)).current;

  const demoSteps = [
    {
      id: 1,
      title: "Step 1: Share Evidence to Lab",
      description: "Press the share button (🔗) on any evidence card to assign it to a lab and department.",
      icon: "share-circle",
      color: "#4CAF50",
      action: "Press Share Button",
      target: "Evidence Card"
    },
    {
      id: 2,
      title: "Step 2: Select Lab & Departments",
      description: "Choose the appropriate lab and departments from the modal that opens.",
      icon: "domain",
      color: "#2196F3",
      action: "Select Lab & Dept",
      target: "Lab Modal"
    },
    {
      id: 3,
      title: "Step 3: Enter Selection Mode",
      description: "Long press on any evidence card that has lab information to enter selection mode.",
      icon: "gesture-tap-hold",
      color: "#FF9800",
      action: "Long Press",
      target: "Evidence Card"
    },
    {
      id: 4,
      title: "Step 4: Select Multiple Evidences",
      description: "Tap on evidence cards to select multiple items. Only evidences with lab info can be selected.",
      icon: "checkbox-multiple-marked-circle",
      color: "#9C27B0",
      action: "Multi-Select",
      target: "Evidence Cards"
    },
    {
      id: 5,
      title: "Step 5: Submit to Forensic",
      description: "Press 'SUBMIT TO FORENSIC' button to send selected evidences. A QR code will be generated.",
      icon: "qrcode-scan",
      color: "#E91E63",
      action: "Submit",
      target: "Forensic Button"
    },
    {
      id: 6,
      title: "Alternative: Submit to Court",
      description: "You can also submit the case directly to judiciary using 'SUBMIT TO JUDICIARY' button.",
      icon: "gavel",
      color: "#795548",
      action: "Submit to Court",
      target: "Judiciary Button"
    }
  ];

  useEffect(() => {
    if (isVisible) {
      setCurrentStep(0);
      setShowTooltip(true);
      startAnimations();
    } else {
      resetAnimations();
    }
  }, [isVisible]);

  const startAnimations = () => {
    Animated.parallel([
      Animated.timing(fadeAnim, {
        toValue: 1,
        duration: 300,
        useNativeDriver: true,
      }),
      Animated.timing(slideAnim, {
        toValue: 0,
        duration: 400,
        useNativeDriver: true,
      }),
      Animated.timing(scaleAnim, {
        toValue: 1,
        duration: 350,
        useNativeDriver: true,
      }),
    ]).start();

    // Start pulse animation
    startPulseAnimation();
  };

  const startPulseAnimation = () => {
    Animated.loop(
      Animated.sequence([
        Animated.timing(pulseAnim, {
          toValue: 1.1,
          duration: 800,
          useNativeDriver: true,
        }),
        Animated.timing(pulseAnim, {
          toValue: 1,
          duration: 800,
          useNativeDriver: true,
        }),
      ])
    ).start();
  };

  const resetAnimations = () => {
    fadeAnim.setValue(0);
    slideAnim.setValue(50);
    scaleAnim.setValue(0.8);
    pulseAnim.setValue(1);
  };

  const nextStep = () => {
    if (currentStep < demoSteps.length - 1) {
      setCurrentStep(currentStep + 1);
    } else {
      onClose();
    }
  };

  const prevStep = () => {
    if (currentStep > 0) {
      setCurrentStep(currentStep - 1);
    }
  };

  const skipDemo = () => {
    onClose();
  };

  const currentStepData = demoSteps[currentStep];

  const renderMockEvidenceCard = () => (
    <View style={styles.mockEvidenceCard}>
      <View style={styles.mockImageContainer}>
        <MaterialCommunityIcons name="image" size={40} color={Colors.lightText} />
      </View>
      <View style={styles.mockEvidenceDetails}>
        <Text style={styles.mockEvidenceTitle}>Sample Evidence</Text>
        <Text style={styles.mockEvidenceType}>Type: Digital</Text>
        <Text style={styles.mockEvidenceStatus}>Status: Ready</Text>
      </View>
      <Animated.View 
        style={[
          styles.mockShareButton,
          { transform: [{ scale: currentStep === 0 ? pulseAnim : 1 }] }
        ]}
      >
        <MaterialCommunityIcons
          name="share-circle"
          size={24}
          color={currentStep === 0 ? "#4CAF50" : Colors.primary}
        />
      </Animated.View>
    </View>
  );

  const renderMockButtons = () => (
    <View style={styles.mockButtonContainer}>
      <Animated.View 
        style={[
          styles.mockButton,
          { 
            backgroundColor: currentStep === 5 ? "#795548" : "#0B36A1",
            transform: [{ scale: currentStep === 5 ? pulseAnim : 1 }]
          }
        ]}
      >
        <Text style={styles.mockButtonText}>SUBMIT TO JUDICIARY</Text>
      </Animated.View>
      <Animated.View 
        style={[
          styles.mockButton,
          { 
            backgroundColor: currentStep === 4 ? "#E91E63" : "#0B36A1",
            transform: [{ scale: currentStep === 4 ? pulseAnim : 1 }]
          }
        ]}
      >
        <Text style={styles.mockButtonText}>SUBMIT TO FORENSIC</Text>
      </Animated.View>
    </View>
  );

  const renderStepIndicator = () => (
    <View style={styles.stepIndicatorContainer}>
      {demoSteps.map((_, index) => (
        <View
          key={index}
          style={[
            styles.stepDot,
            index === currentStep && styles.activeStepDot,
            index < currentStep && styles.completedStepDot,
          ]}
        />
      ))}
    </View>
  );

  return (
    <Modal visible={isVisible} animationType="fade" transparent={true}>
      <View style={styles.overlay}>
        <Animated.View
          style={[
            styles.demoContainer,
            {
              opacity: fadeAnim,
              transform: [
                { translateY: slideAnim },
                { scale: scaleAnim }
              ]
            }
          ]}
        >
          <ScrollView showsVerticalScrollIndicator={false}>
            {/* Header */}
            <View style={styles.header}>
              <View style={styles.headerTitleContainer}>
                <MaterialCommunityIcons 
                  name="school" 
                  size={24} 
                  color={Colors.primary} 
                />
                <Text style={styles.headerTitle}>Evidence Process Guide</Text>
              </View>
              <TouchableOpacity onPress={skipDemo} style={styles.skipButton}>
                <Text style={styles.skipText}>Skip</Text>
              </TouchableOpacity>
            </View>

            {/* Step Indicator */}
            {renderStepIndicator()}

            {/* Current Step */}
            <View style={styles.stepContainer}>
              <View style={[styles.stepIconContainer, { backgroundColor: currentStepData.color }]}>
                <MaterialCommunityIcons 
                  name={currentStepData.icon} 
                  size={32} 
                  color="white" 
                />
              </View>
              
              <Text style={styles.stepTitle}>{currentStepData.title}</Text>
              <Text style={styles.stepDescription}>{currentStepData.description}</Text>
              
              <View style={styles.actionContainer}>
                <View style={styles.actionBadge}>
                  <Text style={styles.actionText}>Action: {currentStepData.action}</Text>
                </View>
                <View style={styles.targetBadge}>
                  <Text style={styles.targetText}>Target: {currentStepData.target}</Text>
                </View>
              </View>
            </View>

            {/* Mock UI Demo */}
            <View style={styles.mockUIContainer}>
              <Text style={styles.mockUITitle}>Visual Demo:</Text>
              
              {/* Show different mock UIs based on current step */}
              {(currentStep === 0 || currentStep === 2 || currentStep === 3) && renderMockEvidenceCard()}
              
              {currentStep === 1 && (
                <View style={styles.mockLabModal}>
                  <Text style={styles.mockModalTitle}>Select Lab & Department</Text>
                  <View style={styles.mockLabOption}>
                    <Text style={styles.mockLabText}>🏥 Forensic Lab Delhi</Text>
                  </View>
                  <View style={styles.mockDeptOption}>
                    <Text style={styles.mockDeptText}>📋 DNA Analysis</Text>
                  </View>
                </View>
              )}
              
              {(currentStep === 4 || currentStep === 5) && renderMockButtons()}
            </View>

            {/* Process Flow Summary */}
            <View style={styles.processFlowContainer}>
              <Text style={styles.processFlowTitle}>Complete Process Flow:</Text>
              <View style={styles.flowSteps}>
                {demoSteps.map((step, index) => (
                  <View key={step.id} style={styles.flowStep}>
                    <View style={[
                      styles.flowStepNumber,
                      { 
                        backgroundColor: index <= currentStep ? step.color : Colors.lightText,
                        opacity: index <= currentStep ? 1 : 0.3 
                      }
                    ]}>
                      <Text style={styles.flowStepNumberText}>{step.id}</Text>
                    </View>
                    <Text style={[
                      styles.flowStepText,
                      { opacity: index <= currentStep ? 1 : 0.5 }
                    ]}>
                      {step.action}
                    </Text>
                    {index < demoSteps.length - 1 && (
                      <MaterialCommunityIcons 
                        name="arrow-down" 
                        size={16} 
                        color={index < currentStep ? Colors.primary : Colors.lightText}
                        style={{ opacity: index < currentStep ? 1 : 0.3 }}
                      />
                    )}
                  </View>
                ))}
              </View>
            </View>
          </ScrollView>

          {/* Navigation */}
          <View style={styles.navigationContainer}>
            <TouchableOpacity
              onPress={prevStep}
              style={[styles.navButton, currentStep === 0 && styles.disabledButton]}
              disabled={currentStep === 0}
            >
              <MaterialCommunityIcons name="arrow-left" size={20} color="white" />
              <Text style={styles.navButtonText}>Previous</Text>
            </TouchableOpacity>

            <Text style={styles.stepCounter}>
              {currentStep + 1} of {demoSteps.length}
            </Text>

            <TouchableOpacity
              onPress={nextStep}
              style={[styles.navButton, styles.nextButton]}
            >
              <Text style={styles.navButtonText}>
                {currentStep === demoSteps.length - 1 ? 'Finish' : 'Next'}
              </Text>
              <MaterialCommunityIcons 
                name={currentStep === demoSteps.length - 1 ? "check" : "arrow-right"} 
                size={20} 
                color="white" 
              />
            </TouchableOpacity>
          </View>
        </Animated.View>
      </View>
    </Modal>
  );
};

const styles = StyleSheet.create({
  overlay: {
    flex: 1,
    backgroundColor: 'rgba(0, 0, 0, 0.8)',
    justifyContent: 'center',
    alignItems: 'center',
  },
  demoContainer: {
    width: width * 0.9,
    maxHeight: height * 0.85,
    backgroundColor: Colors.background,
    borderRadius: 16,
    padding: 0,
  },
  header: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 20,
    borderBottomWidth: 1,
    borderBottomColor: Colors.border,
  },
  headerTitleContainer: {
    flexDirection: 'row',
    alignItems: 'center',
  },
  headerTitle: {
    fontSize: 18,
    fontWeight: 'bold',
    color: Colors.black,
    marginLeft: 8,
    fontFamily: 'Roboto_Bold',
  },
  skipButton: {
    padding: 8,
  },
  skipText: {
    color: Colors.primary,
    fontSize: 16,
    fontFamily: 'Roboto',
  },
  stepIndicatorContainer: {
    flexDirection: 'row',
    justifyContent: 'center',
    paddingVertical: 16,
    paddingHorizontal: 20,
  },
  stepDot: {
    width: 10,
    height: 10,
    borderRadius: 5,
    backgroundColor: Colors.lightText,
    marginHorizontal: 4,
    opacity: 0.3,
  },
  activeStepDot: {
    backgroundColor: Colors.primary,
    opacity: 1,
    transform: [{ scale: 1.2 }],
  },
  completedStepDot: {
    backgroundColor: '#4CAF50',
    opacity: 1,
  },
  stepContainer: {
    padding: 20,
    alignItems: 'center',
  },
  stepIconContainer: {
    width: 64,
    height: 64,
    borderRadius: 32,
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 16,
  },
  stepTitle: {
    fontSize: 20,
    fontWeight: 'bold',
    color: Colors.black,
    textAlign: 'center',
    marginBottom: 8,
    fontFamily: 'Roboto_Bold',
  },
  stepDescription: {
    fontSize: 14,
    color: Colors.lightText,
    textAlign: 'center',
    lineHeight: 20,
    marginBottom: 16,
    fontFamily: 'Roboto',
  },
  actionContainer: {
    flexDirection: 'row',
    gap: 12,
  },
  actionBadge: {
    backgroundColor: '#E3F2FD',
    paddingVertical: 6,
    paddingHorizontal: 12,
    borderRadius: 16,
  },
  actionText: {
    fontSize: 12,
    color: '#1976D2',
    fontWeight: '500',
    fontFamily: 'Roboto',
  },
  targetBadge: {
    backgroundColor: '#F3E5F5',
    paddingVertical: 6,
    paddingHorizontal: 12,
    borderRadius: 16,
  },
  targetText: {
    fontSize: 12,
    color: '#7B1FA2',
    fontWeight: '500',
    fontFamily: 'Roboto',
  },
  mockUIContainer: {
    margin: 20,
    padding: 16,
    backgroundColor: '#F8F9FA',
    borderRadius: 12,
    borderWidth: 1,
    borderColor: Colors.border,
  },
  mockUITitle: {
    fontSize: 14,
    fontWeight: 'bold',
    color: Colors.black,
    marginBottom: 12,
    fontFamily: 'Roboto_Bold',
  },
  mockEvidenceCard: {
    flexDirection: 'row',
    backgroundColor: Colors.background,
    borderWidth: 1,
    borderColor: Colors.border,
    borderRadius: 8,
    padding: 12,
  },
  mockImageContainer: {
    width: 60,
    height: 60,
    backgroundColor: '#F0F0F0',
    borderRadius: 6,
    justifyContent: 'center',
    alignItems: 'center',
    marginRight: 12,
  },
  mockEvidenceDetails: {
    flex: 1,
  },
  mockEvidenceTitle: {
    fontSize: 14,
    fontWeight: 'bold',
    color: Colors.black,
    fontFamily: 'Roboto_Bold',
  },
  mockEvidenceType: {
    fontSize: 12,
    color: Colors.lightText,
    marginTop: 4,
    fontFamily: 'Roboto',
  },
  mockEvidenceStatus: {
    fontSize: 12,
    color: '#4CAF50',
    marginTop: 2,
    fontFamily: 'Roboto',
  },
  mockShareButton: {
    justifyContent: 'center',
    alignItems: 'center',
    padding: 4,
  },
  mockLabModal: {
    backgroundColor: Colors.background,
    borderRadius: 8,
    padding: 16,
    borderWidth: 1,
    borderColor: Colors.border,
  },
  mockModalTitle: {
    fontSize: 16,
    fontWeight: 'bold',
    color: Colors.black,
    marginBottom: 12,
    fontFamily: 'Roboto_Bold',
  },
  mockLabOption: {
    backgroundColor: '#E8F0FE',
    padding: 12,
    borderRadius: 6,
    marginBottom: 8,
    borderWidth: 1,
    borderColor: Colors.primary,
  },
  mockLabText: {
    fontSize: 14,
    color: Colors.primary,
    fontFamily: 'Roboto',
  },
  mockDeptOption: {
    backgroundColor: '#F3E5F5',
    padding: 12,
    borderRadius: 6,
    borderWidth: 1,
    borderColor: '#9C27B0',
  },
  mockDeptText: {
    fontSize: 14,
    color: '#9C27B0',
    fontFamily: 'Roboto',
  },
  mockButtonContainer: {
    flexDirection: 'row',
    gap: 8,
  },
  mockButton: {
    flex: 1,
    backgroundColor: '#0B36A1',
    padding: 12,
    borderRadius: 25,
    alignItems: 'center',
  },
  mockButtonText: {
    color: 'white',
    fontSize: 10,
    fontWeight: 'bold',
    fontFamily: 'Roboto_Bold',
  },
  processFlowContainer: {
    margin: 20,
    padding: 16,
    backgroundColor: '#FAFAFA',
    borderRadius: 12,
    borderWidth: 1,
    borderColor: Colors.border,
  },
  processFlowTitle: {
    fontSize: 14,
    fontWeight: 'bold',
    color: Colors.black,
    marginBottom: 16,
    textAlign: 'center',
    fontFamily: 'Roboto_Bold',
  },
  flowSteps: {
    alignItems: 'center',
  },
  flowStep: {
    alignItems: 'center',
    marginVertical: 4,
  },
  flowStepNumber: {
    width: 24,
    height: 24,
    borderRadius: 12,
    justifyContent: 'center',
    alignItems: 'center',
    marginBottom: 4,
  },
  flowStepNumberText: {
    color: 'white',
    fontSize: 12,
    fontWeight: 'bold',
    fontFamily: 'Roboto_Bold',
  },
  flowStepText: {
    fontSize: 11,
    color: Colors.black,
    textAlign: 'center',
    marginBottom: 8,
    fontFamily: 'Roboto',
  },
  navigationContainer: {
    flexDirection: 'row',
    justifyContent: 'space-between',
    alignItems: 'center',
    padding: 20,
    borderTopWidth: 1,
    borderTopColor: Colors.border,
  },
  navButton: {
    flexDirection: 'row',
    alignItems: 'center',
    backgroundColor: Colors.lightText,
    paddingVertical: 12,
    paddingHorizontal: 16,
    borderRadius: 25,
  },
  nextButton: {
    backgroundColor: Colors.primary,
  },
  disabledButton: {
    backgroundColor: Colors.disabled,
    opacity: 0.5,
  },
  navButtonText: {
    color: 'white',
    fontSize: 14,
    fontWeight: '500',
    marginHorizontal: 4,
    fontFamily: 'Roboto',
  },
  stepCounter: {
    fontSize: 14,
    color: Colors.lightText,
    fontFamily: 'Roboto',
  },
});

export default EvidenceProcessDemo;