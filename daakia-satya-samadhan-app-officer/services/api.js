import axios from 'axios';
import Constants from 'expo-constants';
import { API_ENDPOINTS, AUTH_ERRORS, FILE_TYPES } from '../constants/auth';

const BASE_URL = Constants.expoConfig?.extra?.baseUrl;

if (!BASE_URL) {
  throw new Error(AUTH_ERRORS.BASE_URL_MISSING);
}

const createBaseInstance = (token = '') => {
  const instance = axios.create({
    baseURL: BASE_URL,
    headers: {
      Authorization: token ? `Bearer ${token}` : '',
    },
  });


  instance.interceptors.response.use(
    (response) => response,
    async (error) => {
      if (error.response && (error.response.status === 401 || error.response.status === 403)) {
        throw new Error(AUTH_ERRORS.TOKEN_EXPIRED);
      }
      return Promise.reject(error);
    }
  );

  return instance;
};

// Generic request handler
const makeRequest = async (method, endpoint, data = null, token = '', customHeaders = {}) => {
  const instance = createBaseInstance(token);
  const config = {
    headers: { ...customHeaders },
  };

  try {
    const response = method === 'get' 
      ? await instance[method](endpoint, config)
      : await instance[method](endpoint, data, config);
    return response.data;
  } catch (error) {
    throw error;
  }
};

export const apiService = {
  // ===== AUTHENTICATION & TOKEN MANAGEMENT =====
  login: (mobileNumber) => 
    makeRequest('post', API_ENDPOINTS.LOGIN, { mobileNumber }, '', { 'Category': 'police' }),

  verifyOtp: (userId, requestId, otp) => 
    makeRequest('post', API_ENDPOINTS.VERIFY_OTP, { userId, requestId, otp }),

  refreshToken: (userId, token) => 
    makeRequest('post', API_ENDPOINTS.REFRESH_TOKEN, { userId }, token),

  // ===== USER PROFILE MANAGEMENT =====
  fetchProfile: (token) => 
    makeRequest('get', API_ENDPOINTS.PROFILE, null, token),

  updateProfile: (token, updateData) => 
    makeRequest('put', API_ENDPOINTS.PROFILE, updateData, token),

  // ===== REGISTRATION & POLICE STATION MANAGEMENT =====
  register: (userData) => 
    makeRequest('post', API_ENDPOINTS.REGISTER, userData),

  fetchPoliceStations: () => 
    makeRequest('get', API_ENDPOINTS.POLICE_STATIONS),

  fetchPoliceDepartments: (stationId) => 
    makeRequest('get', `${API_ENDPOINTS.POLICE_DEPARTMENTS}/${stationId}`),

  fetchPoliceRanks: () => 
    makeRequest('get', API_ENDPOINTS.POLICE_RANKS),



  fetchCaseEvents: async (token, caseId) => {
    const endpoint = API_ENDPOINTS.CASE_EVENTS.replace(':caseId', caseId);
    try {
      const response = await fetch(`${BASE_URL}${endpoint}`, {
        method: 'GET',
        headers: {
          'Authorization': `Bearer ${token}`,
          'Content-Type': 'application/json'
        }
      });

      const result = await response.json();
      // console.log('API Response:', result);
      return result;
    } catch (error) {
      console.error('Error fetching case events:', error);
      throw error;
    }
  },



  // ===== FILE UPLOAD MANAGEMENT =====
  uploadFile: async (uri, type) => {
    const formData = new FormData();
    const fileType = FILE_TYPES[type.toUpperCase()] || FILE_TYPES.DEFAULT;

    const file = {
      uri,
      name: `file-${Date.now()}.${fileType.extension}`,
      type: fileType.mimeType,
    };

    formData.append('file', file);

    try {
      const response = await fetch(`${BASE_URL}${API_ENDPOINTS.UPLOAD}`, {
        method: 'POST',
        body: formData,
        headers: {
          'Content-Type': 'multipart/form-data',
        },
      });

      const result = await response.json();
      if (result.status === 'success') {
        return result.data.fileUrl;
      }
      throw new Error(result.message || AUTH_ERRORS.UPLOAD_FAILED);
    } catch (error) {
      throw new Error(error.message || AUTH_ERRORS.UPLOAD_ERROR);
    }
  },

  // ===== DISPATCHER MANAGEMENT =====
  fetchDispatcherRecentCases: (token) => 
    makeRequest('get', API_ENDPOINTS.DISPATCHER_RECENT_CASES, null, token),

  fetchForensicRequestEvidences: (token, forensicRequestId) => 
    makeRequest('get', `${API_ENDPOINTS.FORENSIC_REQUEST_EVIDENCES}/${forensicRequestId}/evidences`, null, token),

  dispatchForensicRequest: (token, forensicRequestId) => 
    makeRequest('put', `${API_ENDPOINTS.FORENSIC_REQUEST_DISPATCH}/${forensicRequestId}`, { status: 'dispatched' }, token),

  // ===== CASE MANAGEMENT =====
  fetchCaseDetails: (token, caseId) => 
    makeRequest('get', `${API_ENDPOINTS.CASE_DETAILS}/${caseId}`, null, token),

  updateCaseById: (token, caseId, updateData) => 
    makeRequest('put', `${API_ENDPOINTS.CASE_DETAILS}/${caseId}`, updateData, token),

  fetchCases: (token, page = 1, limit = 100) => 
    makeRequest('get', `${API_ENDPOINTS.FETCH_CASES}?page=${page}&limit=${limit}`, null, token),

  updateCaseEvidence: (token, caseId, evidenceId, updateData) => 
    makeRequest('put', `${API_ENDPOINTS.CASE_EVIDENCE_UPDATE}/${caseId}/evidences/${evidenceId}`, updateData, token),

  submitCaseToForensic: (token, caseId, evidenceIds) => 
    makeRequest('post', API_ENDPOINTS.CASE_SUBMIT_TO_FORENSIC.replace(':caseId', caseId), { evidences: evidenceIds }, token),

  submitCaseToCourt: (token, caseId, courtId, prosecutorId) => 
    makeRequest('post', API_ENDPOINTS.CASE_SUBMIT_TO_COURT, { caseId, courtId, prosecutorId }, token),

  submitEvidence: (token, caseId, data) => 
    makeRequest('post', `${API_ENDPOINTS.SUBMIT_EVIDENCE}/${caseId}/evidences`, data, token),

  updateCasePackage: (token, caseId, data) => 
    makeRequest('put', `${API_ENDPOINTS.UPDATE_CASE_PACKAGE}/${caseId}`, data, token),

  fetchEvidenceDetails: (token, caseId, evidenceId) => 
    makeRequest('get', `${API_ENDPOINTS.EVIDENCE_DETAILS}/${caseId}/evidences/${evidenceId}`, null, token),

  createCase: (token, data) => 
    makeRequest('post', API_ENDPOINTS.CREATE_CASE, data, token),

  updatePremises: (token, caseId, data) => 
    makeRequest('put', `${API_ENDPOINTS.UPDATE_PREMISES}/${caseId}`, data, token),

  // ===== FORENSIC REPORTS MANAGEMENT =====
  fetchForensicReports: async (token, caseId) => {
    try {
      const response = await makeRequest('get', `${API_ENDPOINTS.FORENSIC_REPORTS}/${caseId}`, null, token);

      // Ensure proper structure and validate response
      if (response && response.status === 'success' && response.data) {
        // Ensure data is an array
        const reports = Array.isArray(response.data) ? response.data : [];

        // Add better error handling for missing nested data
        const processedReports = reports.map(report => ({
          ...report,
          // Ensure nested objects exist to prevent undefined errors
          createdBy: report.createdBy || { name: 'Unknown', category: 'Unknown' },
          forensicRequestId: report.forensicRequestId || { status: 'Unknown', labId: { name: 'Unknown Lab' } },
          attachmentUrl: report.attachmentUrl || [],
        }));

        return {
          ...response,
          data: processedReports
        };
      }

      return response;
    } catch (error) {
      console.error('Error in fetchForensicReports:', error);
      throw error;
    }
  },

  accessReport: async (token, userId, reportId) => {
    try {
      if (!userId || !reportId) {
        throw new Error('User ID and Report ID are required');
      }

      const response = await makeRequest('post', API_ENDPOINTS.FORENSIC_REPORT_ACCESS, { userId, reportId }, token);
      // console.log('Access Report API Response:', response);
      return response;
    } catch (error) {
      // console.error('Error in accessReport:', error);
      throw error;
    }
  },

  checkReportAccessStatus: async (token, reportId) => {
    try {
      if (!reportId) {
        throw new Error('Report ID is required');
      }

      const response = await makeRequest('get', API_ENDPOINTS.FORENSIC_REPORT_ACCESS_STATUS.replace(':reportId', reportId), null, token);
      // console.log(`Access Status API Response for Report ${reportId}:`, response);

      // Handle the case where data is empty array (no request made yet)
      if (response?.status === 'success' && Array.isArray(response.data) && response.data.length === 0) {
        // console.log(`No access request found for report ${reportId} - user can request access`);
      }

      return response;
    } catch (error) {
      // console.error(`Error checking access status for report ${reportId}:`, error);
      // Return a default structure instead of throwing to prevent UI breaks
      return {
        status: 'success',
        data: [], // Empty array indicates no request has been made
        message: 'No access request found'
      };
    }
  },
  
  // ===== COURT MANAGEMENT =====
  fetchCourts: () =>
    makeRequest('get', API_ENDPOINTS.COURTS),

  fetchProsecutors: (token, courtId) =>
    makeRequest('get', API_ENDPOINTS.COURT_PROSECUTORS.replace(':courtId', courtId), null, token),

  // ===== LAB MANAGEMENT =====
  fetchLabs: () =>
    makeRequest('get', API_ENDPOINTS.FETCH_LABS),

  fetchDepartments: (labId) =>
    makeRequest('get', API_ENDPOINTS.FETCH_DEPARTMENTS.replace(':labId', labId)),

  // ===== EVIDENCE TAGS =====
  fetchEvidenceTags: () =>
    makeRequest('get', API_ENDPOINTS.EVIDENCE_TAGS),
};